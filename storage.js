import { TokenModel, UserDataModel } from "./linked_roles/models.js";

export async function storeDiscordTokens(userId, tokens) {
	await TokenModel.findOneAndUpdate({ userId }, { tokens }, { upsert: true });
}

export async function getDiscordTokens(userId) {
	const doc = await TokenModel.findOne({ userId });
	return doc?.tokens || null;
}

export async function getUserData(userId) {
	return await UserDataModel.findOne({ userId });
}

export async function incrementTimelapseCount(userId) {
	await UserDataModel.findOneAndUpdate(
		{ userId },
		{ $inc: { timelapseCount: 1 } },
		{ upsert: true },
	);
}

export async function incrementTicketCount(userId) {
	await UserDataModel.findOneAndUpdate({ userId }, { $inc: { ticketCount: 1 } }, { upsert: true });
}

export async function setTimelapseCount(userId, count) {
	await UserDataModel.findOneAndUpdate({ userId }, { timelapseCount: count }, { upsert: true });
}

export async function setTicketCount(userId, count) {
	await UserDataModel.findOneAndUpdate({ userId }, { ticketCount: count }, { upsert: true });
}
