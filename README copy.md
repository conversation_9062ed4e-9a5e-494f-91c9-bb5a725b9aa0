# Kaeru Redirect Server

This is the Discord Linked Roles server for the Kaeru bot system. It handles OAuth2 authentication and metadata updates for Discord's linked roles feature.

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Copy the environment template
cp .env.template .env

# Edit the .env file with your values
nano .env
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Start the Server

```bash
# Development
npm run dev

# Production
npm start
```

## 📋 Environment Variables

| Variable        | Description                       | Required | Example                                                  |
| --------------- | --------------------------------- | -------- | -------------------------------------------------------- |
| `MONGO_URI`     | MongoDB connection string         | ✅       | `mongodb+srv://user:<EMAIL>/kaeru`      |
| `CLIENT_ID`     | Discord application client ID     | ✅       | `123456789012345678`                                     |
| `CLIENT_SECRET` | Discord application client secret | ✅       | `your_secret_here`                                       |
| `REDIRECT_URI`  | OAuth2 redirect URI               | ✅       | `https://redirect.yourdomain.com/discord-oauth-callback` |
| `COOKIE_SECRET` | Secret for signing cookies        | ✅       | `random_secret_string`                                   |
| `PORT`          | Server port                       | ❌       | `3000` (default)                                         |
| `NODE_ENV`      | Environment mode                  | ❌       | `production`                                             |

## 🔗 Integration with Main Bot

### Database Sharing

- **IMPORTANT**: Use the same `MONGO_URI` as your main Kaeru bot
- Both systems access the same `UserData` collection
- Timelapse and ticket counts are shared between systems

### API Endpoints

The main bot communicates with this server via these endpoints:

- `POST /increment-timelapse` - Increments user's timelapse count
- `POST /set-linkedrole-value` - Sets exact linked role count values (bot owner only)
- `POST /update-metadata` - Updates user's Discord metadata
- `GET /linked-role` - Initiates OAuth2 flow

### Discord Bot Configuration

In your main bot's environment, set:

```env
APP_SERVER_URL=https://redirect.yourdomain.com
```

## 🏗️ Architecture

```
┌─────────────────┐    HTTP API    ┌──────────────────┐
│   Kaeru Bot     │◄──────────────►│ Redirect Server  │
│   (Discord)     │                │  (This Repo)     │
└─────────────────┘                └──────────────────┘
         │                                   │
         │            Shared Database        │
         └──────────────►MongoDB◄────────────┘
```

## 📊 Linked Roles Metadata

The server manages these metadata fields:

| Field           | Condition              | Description                                 |
| --------------- | ---------------------- | ------------------------------------------- |
| `time_master`   | `timelapseCount >= 10` | User has completed 10+ timelapse activities |
| `issue_tracker` | `ticketCount >= 10`    | User has created 10+ support tickets        |

## 🔧 Development

### Local Development

1. Set `REDIRECT_URI=http://localhost:3000/discord-oauth-callback`
2. Configure Discord app with the local redirect URI
3. Use `npm run dev` for auto-restart on changes

### Production Deployment

1. Set `NODE_ENV=production`
2. Use HTTPS for `REDIRECT_URI`
3. Set strong `COOKIE_SECRET`
4. Configure proper CORS if needed

## 🛡️ Security Notes

- Always use HTTPS in production
- Keep `CLIENT_SECRET` and `COOKIE_SECRET` secure
- Regularly rotate secrets
- Monitor for unusual OAuth2 activity

## 📝 API Documentation

### POST /increment-timelapse

Increments a user's timelapse count and updates their metadata.

**Request:**

```json
{
	"userId": "123456789012345678"
}
```

**Response:** `204 No Content` on success

### POST /set-linkedrole-value

Sets exact linked role count values and updates metadata (bot owner only).

**Request:**

```json
{
	"userId": "123456789012345678",
	"roleType": "timelapse",
	"value": 15
}
```

**Parameters:**

- `userId`: Discord user ID
- `roleType`: Either "timelapse" or "issue_tracker"
- `value`: Non-negative integer to set as the exact count

**Response:** `204 No Content` on success

### POST /update-metadata

Updates a user's Discord linked role metadata.

**Request:**

```json
{
	"userId": "123456789012345678"
}
```

**Response:** `204 No Content` on success

## 🔄 Migration from Monorepo

When moving from the main Kaeru repository:

1. Copy the entire `app/` folder to new repository
2. Set up environment variables using `.env.template`
3. Update main bot's `APP_SERVER_URL` to point to new server
4. Deploy both systems independently

No database migration needed - both systems continue using the same MongoDB instance.

## 📞 Support

For issues related to:

- **Discord bot commands**: Check main Kaeru repository
- **Linked roles/OAuth2**: This repository
- **Database issues**: Check connection strings in both repositories
