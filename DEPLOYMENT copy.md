# Kaeru Redirect Server Deployment Guide

This guide helps you deploy the Kaeru Redirect Server as a separate service from the main bot.

## 🎯 Pre-Deployment Checklist

### 1. Environment Preparation
- [ ] Copy `.env.template` to `.env`
- [ ] Fill in all required environment variables
- [ ] Ensure `MONGO_URI` matches your main bot's database
- [ ] Set up Discord application redirect URI

### 2. Discord Developer Portal Setup
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Select your application
3. Go to **OAuth2** → **General**
4. Add your redirect URI: `https://yourdomain.com/discord-oauth-callback`
5. Save changes

## 🚀 Deployment Options

### Option 1: Traditional VPS/Server

```bash
# 1. Clone/copy the app folder to your server
git clone https://github.com/minesa-org/kaeru-redirect.git
cd kaeru-redirect

# 2. Install dependencies
npm install

# 3. Set up environment
cp .env.template .env
nano .env  # Fill in your values

# 4. Start with PM2 (recommended)
npm install -g pm2
pm2 start server.js --name "kaeru-redirect"
pm2 save
pm2 startup
```

### Option 2: Docker Deployment

Create `Dockerfile`:
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000
CMD ["npm", "start"]
```

Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  kaeru-redirect:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    restart: unless-stopped
```

Deploy:
```bash
docker-compose up -d
```

### Option 3: Railway/Heroku/Vercel

1. **Railway:**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Deploy
   railway login
   railway init
   railway up
   ```

2. **Heroku:**
   ```bash
   # Install Heroku CLI
   heroku create kaeru-redirect
   heroku config:set MONGO_URI="your_mongo_uri"
   heroku config:set CLIENT_ID="your_client_id"
   # ... set other env vars
   git push heroku main
   ```

## 🔧 Post-Deployment Configuration

### 1. Update Main Bot Configuration
In your main Kaeru bot's `.env`:
```env
APP_SERVER_URL=https://your-redirect-domain.com
```

### 2. Test the Integration
1. Restart your main bot
2. Try the `/timelapse` command
3. Check logs for successful API calls
4. Test Discord linked roles flow

### 3. SSL/HTTPS Setup (Required for Production)
Discord requires HTTPS for OAuth2 redirects.

**With Nginx:**
```nginx
server {
    listen 443 ssl;
    server_name your-redirect-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**With Cloudflare:**
1. Add your domain to Cloudflare
2. Enable SSL/TLS encryption
3. Set SSL mode to "Full" or "Full (strict)"

## 📊 Monitoring and Logs

### Health Check Endpoint
The server provides a health check at:
```
GET https://your-domain.com/
Response: "👋"
```

### Log Monitoring
```bash
# PM2 logs
pm2 logs kaeru-redirect

# Docker logs
docker-compose logs -f

# System logs
tail -f /var/log/kaeru-redirect.log
```

### Key Metrics to Monitor
- Response time for `/increment-timelapse` endpoint
- OAuth2 success/failure rates
- Database connection status
- Memory and CPU usage

## 🛡️ Security Best Practices

### 1. Environment Variables
- Never commit `.env` files
- Use strong, random `COOKIE_SECRET`
- Rotate secrets regularly

### 2. Network Security
- Use HTTPS only
- Configure firewall rules
- Limit database access to specific IPs

### 3. Rate Limiting (Optional)
Add rate limiting to prevent abuse:
```javascript
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

app.use(limiter);
```

## 🔄 Backup and Recovery

### Database Backups
Since you share the database with the main bot, ensure regular backups:
```bash
# MongoDB backup
mongodump --uri="your_mongo_uri" --out=/backup/$(date +%Y%m%d)
```

### Application Backups
- Keep your `.env` file backed up securely
- Version control your code
- Document your deployment configuration

## 🚨 Troubleshooting

### Common Issues

**1. OAuth2 Redirect Mismatch**
- Ensure `REDIRECT_URI` in `.env` matches Discord Developer Portal
- Check for trailing slashes or protocol mismatches

**2. Database Connection Issues**
- Verify `MONGO_URI` is identical to main bot
- Check network connectivity and firewall rules
- Ensure MongoDB allows connections from new server IP

**3. API Communication Failures**
- Verify `APP_SERVER_URL` in main bot points to correct domain
- Check HTTPS/SSL configuration
- Monitor server logs for errors

**4. Linked Roles Not Updating**
- Check Discord application permissions
- Verify user has connected their account
- Check metadata update logs

### Debug Mode
Enable debug logging:
```env
NODE_ENV=development
LOG_LEVEL=debug
```

## 📞 Support

For deployment issues:
1. Check server logs first
2. Verify environment variables
3. Test database connectivity
4. Check Discord Developer Portal settings

Remember: The main bot and redirect server are independent but share the same database!
